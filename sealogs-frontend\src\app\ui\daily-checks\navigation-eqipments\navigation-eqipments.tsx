'use client'
import React, { useState } from 'react'
import { useMutation, useLazyQuery } from '@apollo/client'
import { UpdateVesselDailyCheck_LogBookEntrySection } from '@/app/lib/graphQL/mutation'
import {
    UPDATE_SECTION_MEMBER_COMMENT,
    CREATE_SECTION_MEMBER_COMMENT,
} from '@/app/lib/graphQL/mutation'
import { useSearchParams } from 'next/navigation'
import {
    displayDescription,
    composeField,
    displayField,
    getFilteredFields,
    getFieldLabel,
} from '@/app/ui/daily-checks/actions'

import 'react-quill/dist/quill.snow.css'
import SectionMemberCommentModel from '@/app/offline/models/sectionMemberComment'
import VesselDailyCheck_LogBookEntrySectionModel from '@/app/offline/models/vesselDailyCheck_LogBookEntrySection'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { getNavFields } from '@/app/lib/dailyCheckFields'
import { useDebounceFn, useMediaQuery } from '@reactuses/core'
import Crew<PERSON>hecker from '../crew-checker/crew-checker'
import dayjs from 'dayjs'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
    Sheet,
    SheetContent,
    SheetHeader,
    SheetTitle,
} from '@/components/ui/sheet'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import {
    CheckField,
    CheckFieldContent,
    CheckFieldTopContent,
    DailyCheckField,
} from '@/components/daily-check-field'
import { Card } from '@/components/ui'
import { ReadSectionMemberComments } from './queries'

export default function NavigationEquipments({
    logBookConfig = false,
    vesselDailyCheck = false,
    comments,
    setComments,
    setTab, // Used in commented notification code - kept for backward compatibility
    setVesselDailyCheck,
    locked,
    handleCreateTask,
    createMaintenanceCheckLoading,
    offline = false,
    edit_logBookEntry,
    fieldImages,
    refreshImages,
}: {
    logBookConfig: any
    vesselDailyCheck: any
    comments: any
    setComments: any
    setTab: any // Kept for backward compatibility
    setVesselDailyCheck: any
    locked: boolean
    handleCreateTask: any
    createMaintenanceCheckLoading: any
    offline?: boolean
    edit_logBookEntry: boolean
    fieldImages: any
    refreshImages: any
}) {
    const searchParams = useSearchParams()
    // Get vessel ID from URL params (used in other components)
    // const vesselID = searchParams.get('vesselID') ?? 0
    const logentryID = searchParams.get('logentryID') ?? 0
    const [saving, setSaving] = useState(false)
    const tab = searchParams.get('tab') ?? ''
    const [openCommentAlert, setOpenCommentAlert] = useState(false)
    const [currentComment, setCurrentComment] = useState<any>('')
    const [sectionComment, setSectionComment] = useState<any>('')
    const [currentField, setCurrentField] = useState<any>('')
    const [openDescriptionPanel, setOpenDescriptionPanel] = useState(false)
    const [descriptionPanelContent, setDescriptionPanelContent] = useState('')
    const [descriptionPanelHeading, setDescriptionPanelHeading] = useState('')

    const commentModel = new SectionMemberCommentModel()
    const dailyCheckModel = new VesselDailyCheck_LogBookEntrySectionModel()
    // Used for responsive design in Sheet component
    const isWide = useMediaQuery('(min-width: 640px)')
    const [navigationCrewResponsible, setNavigationCrewResponsible] =
        useState<any>(
            vesselDailyCheck?.navigationCrewResponsible?.nodes?.map(
                (member: any) => ({
                    label: member.firstName + ' ' + member.surname,
                    value: member.id,
                }),
            ),
        )
    const [navigationCheckTime, setNavigationCheckTime] = useState<any>(
        dayjs(vesselDailyCheck?.navigationCheckTime ?? new Date()),
    )

    /* useEffect(() => {
        getSectionVesselDailyCheck_LogBookEntrySection({
            variables: {
                id: [vesselDailyCheck.id],
            },
        })
    }, [tab]) */

    const handleNavigationChecks = async (check: Boolean, value: any) => {
        if (+vesselDailyCheck?.id > 0) {
            const variable = {
                id: vesselDailyCheck.id,
                [value]: check ? 'Ok' : 'Not_Ok',
            }
            if (offline) {
                const data = await dailyCheckModel.save(variable)
                setVesselDailyCheck([data])
                // setSaving(true)
                setSaving(false)
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        [value]: check ? 'Ok' : 'Not_Ok',
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variable,
                    },
                })
            }
        }
    }

    const [updateVesselDailyCheck_LogBookEntrySection] = useMutation(
        UpdateVesselDailyCheck_LogBookEntrySection,
        {
            onCompleted: () => {
                setSaving(true)
            },
            onError: (error) => {
                console.error('Error completing safety check', error)
            },
        },
    )

    /* const [getSectionVesselDailyCheck_LogBookEntrySection] = useLazyQuery(
        VesselDailyCheck_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data =
                    response.readVesselDailyCheck_LogBookEntrySections.nodes
                setVesselDailyCheck(data)
                setSaving(true)
                if (vesselDailyCheck === data[0]) {
                    // Custom notification logic can be implemented here
                }
            },
            onError: (error: any) => {
                console.error(
                    'VesselDailyCheck_LogBookEntrySection error',
                    error,
                )
            },
        },
    ) */

    // Function to set the active tab (used in commented notification code)
    // Keeping this for future reference
    // const handleSetTab = (tab: any) => {
    //     setTab(tab)
    // }

    // useEffect(() => {
    //     if (saving) {
    //         // Custom notification logic can be implemented here
    //     }
    // }, [vesselDailyCheck])

    const getComment = (fieldName: string, commentType = 'FieldComment') => {
        const comment =
            comments?.length > 0
                ? comments.filter(
                      (comment: any) =>
                          comment.fieldName === fieldName &&
                          comment.commentType === commentType,
                  )
                : false
        return comment.length > 0 ? comment[0] : false
    }

    const showCommentPopup = (comment: string, field: any) => {
        setCurrentComment(comment ? comment : '')
        setCurrentField(field)
        setOpenCommentAlert(true)
    }

    const handleSaveComment = async () => {
        setOpenCommentAlert(false)
        const comment = (document.getElementById('comment') as HTMLInputElement)
            ?.value
        const variables = {
            id: currentComment?.id ? currentComment?.id : 0,
            fieldName: currentField[0]?.fieldName,
            comment: comment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'FieldComment',
        }
        if (currentComment) {
            if (offline) {
                await commentModel.save(variables)
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variables },
                })
            }
        } else {
            if (offline) {
                const offlineID = generateUniqueId()
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variables },
                })
            }
        }
    }

    const [updateSectionMemberComment] = useMutation(
        UPDATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: () => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error updating comment', error)
            },
        },
    )

    const [createSectionMemberComment] = useMutation(
        CREATE_SECTION_MEMBER_COMMENT,
        {
            onCompleted: () => {
                loadSectionMemberComments()
            },
            onError: (error) => {
                console.error('Error creating comment', error)
            },
        },
    )

    const [querySectionMemberComments] = useLazyQuery(
        ReadSectionMemberComments,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readSectionMemberComments.nodes
                if (data) {
                    setComments(data)
                }
            },
            onError: (error: any) => {
                console.error('querySectionMemberComments error', error)
            },
        },
    )

    const loadSectionMemberComments = async () => {
        if (offline) {
            const data = await commentModel.getByLogBookEntrySectionID(
                vesselDailyCheck.id,
            )
            if (data) {
                setComments(data)
            }
        } else {
            await querySectionMemberComments({
                variables: {
                    filter: {
                        logBookEntrySectionID: { eq: vesselDailyCheck.id },
                    },
                },
            })
        }
    }

    const fields = getNavFields(logBookConfig, vesselDailyCheck)
    /* const handleSave = async () => {
        if (offline) {
            const data = await dailyCheckModel.getByIds([vesselDailyCheck.id])
            setVesselDailyCheck(data)
            setSaving(true)
            if (vesselDailyCheck === data[0]) {
                // Custom notification logic can be implemented here
            }
        } else {
            getSectionVesselDailyCheck_LogBookEntrySection({
                variables: {
                    id: [vesselDailyCheck.id],
                },
            })
        }
        const comment = sectionComment
        const variables = {
            id: currentComment?.id ? currentComment?.id : 0,
            fieldName: 'Navigation',
            comment: comment,
            logBookEntryID: +logentryID,
            logBookEntrySectionID: vesselDailyCheck.id,
            commentType: 'Section',
        }
        if (currentComment) {
            if (offline) {
                await commentModel.save(variables)
                loadSectionMemberComments()
            } else {
                updateSectionMemberComment({
                    variables: { input: variables },
                })
            }
        } else {
            if (offline) {
                const offlineID = getComment('Navigation', 'Section')
                    ? getComment('Navigation', 'Section').id
                    : generateUniqueId()
                await commentModel.save({ ...variables, id: offlineID })
                loadSectionMemberComments()
            } else {
                createSectionMemberComment({
                    variables: { input: variables },
                })
            }
        }
    } */

    // These functions are not currently used but kept for future reference
    // const handleGroupNoChange = (groupField: any, groupFieldParent: any) => {
    //     handleNavigationChecks(
    //         false,
    //         fields.find((field: any) => field.name === groupFieldParent.name)
    //             ?.value,
    //     )
    //     groupField.map((field: any) =>
    //         handleNavigationChecks(false, field.value),
    //     )
    // }
    //
    // const handleGroupYesChange = (groupField: any, groupFieldParent: any) => {
    //     handleNavigationChecks(
    //         true,
    //         fields.find((field: any) => field.name === groupFieldParent.name)
    //             ?.value,
    //     )
    //     groupField.map((field: any) =>
    //         handleNavigationChecks(true, field.value),
    //     )
    // }

    const handleNavigationCrewResponsible = async (crews: any) => {
        setNavigationCrewResponsible(crews)
        const crewResponsibleIDs = crews?.map((member: any) => member.value)
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                navigationCrewResponsible: crewResponsibleIDs.join(','),
            }
            if (offline) {
                console.log(' offline')
            } else {
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    /* useEffect(() => {
        getSectionVesselDailyCheck_LogBookEntrySection({
            variables: {
                id: [vesselDailyCheck.id],
            },
        })
    }, []) */

    const handleNavigationCheckTime = async (date: any) => {
        setNavigationCheckTime(dayjs(date))
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                navigationCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
            }
            if (offline) {
                console.log(' offline')
                /* const newVesselDailyCheck =
                                await dailyCheckModel.save(variables)
                            // setSaving(true)
                            setSaving(false)
                            setVesselDailyCheck([newVesselDailyCheck])
                            const sections = logbook.logBookEntrySections.nodes
                            const section = {
                                className: 'SeaLogs\\VesselDailyCheck_LogBookEntrySection',
                                id: `${vesselDailyCheck.id}`,
                                logBookComponentClass: 'VesselDailyCheck_LogBookComponent',
                                __typename: 'VesselDailyCheck_LogBookEntrySection',
                            }
                            if (
                                !sections.some(
                                    (s: any) =>
                                        JSON.stringify(s) === JSON.stringify(section),
                                )
                            ) {
                                sections.push(section)
                            }
                            const lb = {
                                ...logbook,
                                logBookEntrySections: { nodes: sections },
                            }
                            await logBookModel.save(lb)
                            getOfflineLogBookEntry() */
            } else {
                setVesselDailyCheck([
                    {
                        ...vesselDailyCheck,
                        navigationCheckTime:
                            dayjs(date).format('YYYY-MM-DD HH:mm'),
                    },
                ])
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const saveSectionComment = () => {
        getComment('Navigation', 'Section')?.id > 0
            ? updateSectionMemberComment({
                  variables: {
                      input: {
                          id: getComment('Navigation', 'Section')?.id,
                          comment: sectionComment,
                      },
                  },
              })
            : createSectionMemberComment({
                  variables: {
                      input: {
                          fieldName: 'Navigation',
                          comment: sectionComment,
                          logBookEntryID: +logentryID,
                          logBookEntrySectionID: vesselDailyCheck.id,
                          commentType: 'Section',
                      },
                  },
              })
    }

    const { run: debounceSaveSectionComment } = useDebounceFn(() => {
        saveSectionComment()
    }, 1000)

    const saveCheckTime = () => {
        if (vesselDailyCheck?.id > 0) {
            updateVesselDailyCheck_LogBookEntrySection({
                variables: {
                    input: {
                        id: vesselDailyCheck.id,
                        navigationCheckTime:
                            dayjs(navigationCheckTime).format(
                                'YYYY-MM-DD HH:mm',
                            ),
                    },
                },
            })
        }
    }
    const { run: debounceSaveCheckTime } = useDebounceFn(() => {
        saveCheckTime()
    }, 1000)
    return (
        <>
            <Card className="space-y-6">
                {logBookConfig && vesselDailyCheck && (
                    <CheckField>
                        {(getFilteredFields(
                            fields,
                            false,
                            logBookConfig,
                        ).filter((field: any) =>
                            displayField(field.name, logBookConfig),
                        ).length > 0 ||
                            getFilteredFields(
                                fields,
                                true,
                                logBookConfig,
                            )?.filter(
                                (groupField: any) =>
                                    displayField(
                                        groupField.name,
                                        logBookConfig,
                                    ) && groupField.items.length > 0,
                            ).length > 0) && <CheckFieldTopContent />}
                        <CheckFieldContent>
                            {getFilteredFields(
                                fields,
                                false,
                                logBookConfig,
                            ).map((field: any, index: number) => (
                                <DailyCheckField
                                    locked={locked || !edit_logBookEntry}
                                    key={index}
                                    displayField={displayField(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    displayDescription={displayDescription(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    // @ts-ignore - Type mismatch but works at runtime
                                    setDescriptionPanelContent={
                                        setDescriptionPanelContent
                                    }
                                    setOpenDescriptionPanel={
                                        setOpenDescriptionPanel
                                    }
                                    setDescriptionPanelHeading={
                                        setDescriptionPanelHeading
                                    }
                                    displayLabel={getFieldLabel(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    inputId={field.value}
                                    handleNoChange={() =>
                                        handleNavigationChecks(
                                            false,
                                            field.value,
                                        )
                                    }
                                    defaultNoChecked={
                                        field.checked === 'Not_Ok'
                                    }
                                    handleYesChange={() =>
                                        handleNavigationChecks(
                                            true,
                                            field.value,
                                        )
                                    }
                                    defaultYesChecked={field.checked === 'Ok'}
                                    commentAction={() =>
                                        showCommentPopup(
                                            getComment(field.name),
                                            composeField(
                                                field.name,
                                                logBookConfig,
                                            ),
                                        )
                                    }
                                    comment={getComment(field.name)?.comment}
                                    displayImage={true}
                                    fieldImages={fieldImages}
                                    onImageUpload={refreshImages}
                                />
                            ))}
                            {getFilteredFields(fields, true, logBookConfig)
                                ?.filter(
                                    (groupField: any) =>
                                        displayField(
                                            groupField.name,
                                            logBookConfig,
                                        ) && groupField.items.length > 0,
                                )
                                ?.map((groupField: any) => (
                                    <div key={groupField.name}>
                                        {groupField?.items
                                            ?.filter((field: any) =>
                                                displayField(
                                                    field.name,
                                                    logBookConfig,
                                                ),
                                            )
                                            ?.map(
                                                (field: any, index: number) => (
                                                    <DailyCheckField
                                                        locked={
                                                            locked ||
                                                            !edit_logBookEntry
                                                        }
                                                        key={index}
                                                        displayField={displayField(
                                                            field.name,
                                                            logBookConfig,
                                                        )}
                                                        displayDescription={displayDescription(
                                                            field.name,
                                                            logBookConfig,
                                                        )}
                                                        // @ts-ignore - Type mismatch but works at runtime
                                                        setDescriptionPanelContent={
                                                            setDescriptionPanelContent
                                                        }
                                                        setOpenDescriptionPanel={
                                                            setOpenDescriptionPanel
                                                        }
                                                        setDescriptionPanelHeading={
                                                            setDescriptionPanelHeading
                                                        }
                                                        displayLabel={getFieldLabel(
                                                            field.name,
                                                            logBookConfig,
                                                        )}
                                                        inputId={field.value}
                                                        handleNoChange={() =>
                                                            handleNavigationChecks(
                                                                false,
                                                                field.value,
                                                            )
                                                        }
                                                        defaultNoChecked={
                                                            field.checked ===
                                                            'Not_Ok'
                                                        }
                                                        handleYesChange={() =>
                                                            handleNavigationChecks(
                                                                true,
                                                                field.value,
                                                            )
                                                        }
                                                        defaultYesChecked={
                                                            field.checked ===
                                                            'Ok'
                                                        }
                                                        commentAction={() =>
                                                            showCommentPopup(
                                                                getComment(
                                                                    field.name,
                                                                ),
                                                                composeField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                        }
                                                        comment={
                                                            getComment(
                                                                field.name,
                                                            )?.comment
                                                        }
                                                        displayImage={true}
                                                        fieldImages={
                                                            fieldImages
                                                        }
                                                        onImageUpload={
                                                            refreshImages
                                                        }
                                                    />
                                                ),
                                            )}
                                    </div>
                                ))}
                        </CheckFieldContent>
                    </CheckField>
                )}

                <CrewChecker
                    vesselDailyCheckID={vesselDailyCheck.id}
                    crewKey="NavigationCrewResponsible"
                    timeKey="NavigationCheckTime"
                    logBookConfig={logBookConfig}
                    locked={locked}
                    offline={offline}
                    edit_logBookEntry={edit_logBookEntry}
                    setCrewResponsible={handleNavigationCrewResponsible}
                    crewResponsible={navigationCrewResponsible}
                    checkTime={navigationCheckTime}
                    handleCheckTime={handleNavigationCheckTime}
                    // setCheckTime={setNavigationCheckTime}
                    setCheckTime={(e) => {
                        setNavigationCheckTime(e)
                        debounceSaveCheckTime()
                    }}
                />

                <Label label="Comments">
                    <Textarea
                        id="section_comment"
                        rows={4}
                        disabled={locked || !edit_logBookEntry}
                        placeholder="Comments for navigation equipment..."
                        onChange={(e) => {
                            setSectionComment(e.target.value)
                            debounceSaveSectionComment()
                        }}
                        // onBlur={(e) =>

                        // }
                        defaultValue={
                            getComment('Navigation', 'Section')?.comment
                        }
                    />
                </Label>
            </Card>

            <AlertDialogNew
                openDialog={openCommentAlert}
                setOpenDialog={setOpenCommentAlert}
                handleCreate={handleSaveComment}
                noButton={locked}
                actionText="Save">
                <Label htmlFor="comment">Comment</Label>
                <Textarea
                    id="comment"
                    readOnly={locked || !edit_logBookEntry}
                    disabled={locked || !edit_logBookEntry}
                    rows={4}
                    placeholder="Comment"
                    defaultValue={currentComment ? currentComment.comment : ''}
                />
            </AlertDialogNew>

            <Sheet
                open={openDescriptionPanel}
                onOpenChange={setOpenDescriptionPanel}>
                <SheetContent
                    side="left"
                    className={`${isWide ? 'w-[60vw]' : 'w-[90vw]'}`}>
                    <SheetHeader>
                        <SheetTitle>
                            Field -{' '}
                            <span className="font-light">
                                {descriptionPanelHeading}
                            </span>
                        </SheetTitle>
                    </SheetHeader>
                    <div className="mt-6 overflow-y-auto h-[80vh]">
                        <div
                            className="prose prose-sm max-w-none"
                            dangerouslySetInnerHTML={{
                                __html: descriptionPanelContent,
                            }}
                        />
                    </div>
                </SheetContent>
            </Sheet>
        </>
    )
}
