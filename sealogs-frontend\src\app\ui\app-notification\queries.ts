import gql from 'graphql-tag'

export const ReadAppNotificationRecipients = gql`
    query ReadAppNotificationRecipients(
        $filter: AppNotificationRecipientFilterFields = {}
        $limit: Int = 100
        $offset: Int = 0
    ) {
        readAppNotificationRecipients(
            filter: $filter
            limit: $limit
            offset: $offset
            sort: { isRead: ASC, created: DESC }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                isRead
                notificationID
                notification {
                    id
                    title
                    message
                    targetLink
                }
            }
        }
    }
`

export const UpdateAppNotificationRecipient = gql`
    mutation UpdateAppNotificationRecipient(
        $input: UpdateAppNotificationRecipientInput!
    ) {
        updateAppNotificationRecipient(input: $input) {
            id
            isRead
        }
    }
`

export const DeleteAppNotificationRecipients = gql`
    mutation DeleteAppNotificationRecipients($ids: [ID]!) {
        deleteAppNotificationRecipients(ids: $ids)
    }
`
