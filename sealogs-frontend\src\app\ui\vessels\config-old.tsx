'use client'

import { useEffect, useMemo, useState } from 'react'
import vesselTypes from '@/app/lib/vesselTypes'
import {
    UPDATE_CUSTOMISED_COMPONENT_FIELD,
    UPDATE_CUSTOMISED_LOGBOOK_COMPONENT,
    CREATE_CUSTOMISED_COMPONENT_FIELD,
    CREATE_CUSTOMISED_LOGBOOK_COMPONENT,
    UPDATE_CUSTOMISED_LOGBOOK_CONFIG,
    CreateCustomisedLogBookConfig,
} from '@/app/lib/graphQL/mutation'
import { useLazyQuery, useMutation } from '@apollo/client'
import { GET_LOGBOOK_CONFIG } from '@/app/lib/graphQL/query'
import { getVesselByID } from '@/app/lib/actions'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import { isCrew } from '@/app/helpers/userHelper'
import {
    filterByVesselType,
    sortTabs,
    isCategorised,
    getComponentsNotInConfig,
    sortCustomisedComponent<PERSON>ields,
    getTabTitle,
} from './actions'
import CustomisedComponentFieldModel from '@/app/offline/models/customisedComponentField'
import CustomisedLogBookConfigModel from '@/app/offline/models/customisedLogBookConfig'
import { uniqueLogbookComponents } from '@/app/helpers/logBookHelper'
import {
    LogBookConfiguration,
    SLALL_LogBookFields,
} from '@/app/lib/logbook-configuration'
import {
    Button,
    Card,
    CardContent,
    CardHeader,
    CardTitle,
    ListHeader,
    Tabs,
    TabsContent,
    TabsList,
    TabsTrigger,
} from '@/components/ui'
import { FooterWrapper } from '@/components/footer-wrapper'
import {
    ArrowLeft,
    BanIcon,
    FilePlusIcon,
    RotateCcw,
    SaveIcon,
} from 'lucide-react'
import {
    EditConfigDialog,
    useEditConfigDialog,
    useDescriptionDialog,
    type IConfigForm,
    type ITab,
    ComponentsNotInConfig,
    LogBookConfigTabContent,
} from './components/logbook-config'
import { DescriptionDialog } from './components/logbook-config/description-dialog'

export default function LogbookConfigOld({
    logBookID,
    vesselID,
}: {
    logBookID: number
    vesselID: number
}) {
    const router = useRouter()
    const [isLoading, setIsLoading] = useState(true)
    const [logBookConfig, setLogBookConfig] = useState<any>(false)
    const [updatedFields, setUpdatedFields] = useState<any>([])
    const [updatedLocalFields, setUpdatedLocalFields] = useState<any>([])
    const [vessel, setVessel] = useState<any>(false)
    const [tab, setTab] = useState<string>('')
    const [tabs, setTabs] = useState<ITab[]>([])
    const [dailyCheckCategory, setDailyCheckCategory] = useState<any>(false)
    const [dailyCheckCategories, setDailyCheckCategories] = useState<any>(false)
    const [levelThreeCategories, setLevelThreeCategories] = useState<any>(false)
    const [levelThreeCategory, setLevelThreeCategory] = useState<any>(false)
    const [resetCounter, setResetCounter] = useState(-1)
    const [documents, setDocuments] = useState<Array<Record<string, any>>>([])
    const [saving, setSaving] = useState(false)
    const [notify, setNotify] = useState(true)
    const [imCrew, setImCrew] = useState(false)
    const [filteredFields, setFilteredFields] = useState<
        LogBookConfiguration[] | null
    >(null)

    const editConfigDialog = useEditConfigDialog()
    const descriptionDialog = useDescriptionDialog()

    const handleSetVessel = (vessel: any) => {
        if (vessel?.vesselType) {
            const logbookFields = SLALL_LogBookFields.filter((field: any) => {
                if (field?.items?.length > 0) {
                    return field.vesselType.includes(
                        vesselTypes.indexOf(vessel?.vesselType),
                    )
                }
                return false
            })
            var filteredFields: any = []
            logbookFields.map((logbookField: any) => {
                var currentField = logbookField
                var currentFieldItems: any = []
                logbookField.items.map((fields: any) => {
                    if (
                        fields.vesselType.includes(
                            vesselTypes.indexOf(vessel?.vesselType),
                        )
                    ) {
                        if (
                            vessel?.vesselSpecifics?.carriesDangerousGoods ==
                            false
                        ) {
                            if (fields.classes !== 'dangerous-goods-sailing') {
                                currentFieldItems.push(fields)
                            }
                        } else {
                            currentFieldItems.push(fields)
                        }
                    }
                    // if (fields.hasDynamicChildren) {
                    //     const dynamicChildren =
                    //         vessel.defaultRadioLogs.nodes.map(
                    //             (radioLog: any) => {
                    //                 return {
                    //                     value: radioLog.title,
                    //                     label: radioLog.title,
                    //                     vesselType: [
                    //                         0, 1, 2, 3, 4, 5, 6, 7, 8, 9,
                    //                     ],
                    //                     status: 'Required',
                    //                     groupTo: 'RadioLog',
                    //                 }
                    //             },
                    //         )
                    //     currentFieldItems.push(...dynamicChildren)
                    // }
                })
                currentField.items = currentFieldItems
                filteredFields.push(currentField)
            })
            // check on this
            setFilteredFields(filteredFields)
        }
        setVessel(vessel)
        loadLogBookConfig()
    }

    getVesselByID(vesselID, handleSetVessel)

    const slallFields = useMemo(() => {
        return filteredFields ? filteredFields : SLALL_LogBookFields
    }, [filteredFields])

    const [queryLogBookConfig] = useLazyQuery(GET_LOGBOOK_CONFIG, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            /**
             * Removes duplicate components from the customisedLogBookComponents array based on the componentClass property.
             * If a duplicate is found, it keeps the component with the highest id value.
             * @param {Object} responseData - The response data containing customisedLogBookComponents.
             * @param {Array} responseData.customisedLogBookComponents.nodes - The array of components to be deduplicated.
             * @returns {Array} The unique components with the highest id value for each componentClass.
             */
            const responseData = response.readOneCustomisedLogBookConfig
            /* const customisedLogBookComponents =
                responseData?.customisedLogBookComponents?.nodes ?? []
            let uniqueComponents = customisedLogBookComponents.reduce(
                (acc: any, current: any) => {
                    const existing = acc.find(
                        (item: any) =>
                            item.componentClass === current.componentClass,
                    )
                    if (existing) {
                        if (Number(current.id) > Number(existing.id)) {
                            return acc.map((item: any) =>
                                item.componentClass === current.componentClass
                                    ? current
                                    : item,
                            )
                        }
                        return acc
                    }
                    return [...acc, current]
                },
                [],
            )
            uniqueComponents = uniqueComponents.map((uniqueComponent: any) => {
                const matchingLogBookField = SLALL_LogBookFields.find(
                    (logBookField: any) =>
                        logBookField.componentClass ===
                        uniqueComponent.componentClass,
                )
                if (matchingLogBookField) {
                    return {
                        ...uniqueComponent,
                        title: matchingLogBookField.label,
                    }
                }
                return uniqueComponent
            }) */
            let uniqueComponents = uniqueLogbookComponents(responseData)
            uniqueComponents = uniqueComponents.map((uniqueComponent: any) => {
                if (uniqueComponent.customisedComponentFields.nodes) {
                    uniqueComponent.customisedComponentFields.nodes.sort(
                        sortCustomisedComponentFields,
                    )
                }
                return uniqueComponent
            })
            responseData.customisedLogBookComponents.nodes = uniqueComponents

            const data = filterByVesselType(
                responseData,
                slallFields,
                vesselTypes,
                vessel,
            )

            if (data) {
                setUpdatedLocalFields([])
                setUpdatedFields([])

                setLogBookConfig(data)
                {
                    data.policies.nodes.length > 0 &&
                        setDocuments(data.policies.nodes)
                }
                if (!tab) {
                    const tabs = data.customisedLogBookComponents?.nodes
                        .map((component: any) => ({
                            title: component.title,
                            category: component.category,
                            componentClass: component.componentClass,
                        }))
                        .sort()
                    const logbookFields = slallFields
                    const config = data.customisedLogBookComponents.nodes
                    const defaultConfig = logbookFields.map(
                        (component: any) => component,
                    )
                    var componentsNotInConfig: any = []
                    defaultConfig.forEach((defaultLogBookComponents: any) => {
                        var found = false
                        config.forEach((customisedLogBookComponents: any) => {
                            if (
                                customisedLogBookComponents.componentClass ===
                                defaultLogBookComponents.componentClass
                            ) {
                                found = true
                            }
                        })
                        if (!found) {
                            componentsNotInConfig.push(defaultLogBookComponents)
                        }
                    })
                    const additionalTabs = componentsNotInConfig.map(
                        (component: any) => ({
                            title: component.label,
                            category: component.category,
                            componentClass: component.componentClass,
                        }),
                    )
                    const sortedTabs = sortTabs(
                        [...tabs, ...additionalTabs],
                        slallFields,
                    )

                    setTabs(sortedTabs)
                    setTab(sortedTabs[0].title)

                    // const categoryTabs: string[] = Array.from(
                    //     new Set<string>(
                    //         data.customisedLogBookComponents?.nodes.map(
                    //             (component: any) => component.category,
                    //         ),
                    //     ),
                    // )

                    // setCategoryTabs(categoryTabs)
                    // setCategoryTab(categoryTabs[0])

                    // console.info('Category Tabs', categoryTabs)

                    // var currentTab = false
                    // sortedTabs.forEach((element: any) => {
                    //     if (element.category === categoryTabs[0]) {
                    //         if (!currentTab) {
                    //             console.info('Set Tab 2', element.title)
                    //             setTab(element.title)
                    //         }
                    //         currentTab = element.title
                    //     }
                    // })
                }
            } else {
                document.body.style.cursor = 'wait'
                createCustomisedLogBookConfig({
                    variables: {
                        input: {
                            customisedLogBookID: logBookID,
                        },
                    },
                })
            }
        },
        onError: (error: any) => {
            console.error('queryLogBookConfig error', error)
        },
    })

    const [createCustomisedLogBookConfig] = useMutation(
        CreateCustomisedLogBookConfig,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.createCustomisedLogBookConfig
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    useEffect(() => {
        if (isLoading && vessel) {
            setImCrew(isCrew() || false)
            setIsLoading(false)
            handleSetDailyCheckCategories()
            handleLevelThreeCategories()
        }
    }, [isLoading, vessel])

    const loadLogBookConfig = async () => {
        await queryLogBookConfig({
            variables: {
                id: +logBookID,
            },
        })
    }

    const [createCustomisedComponentField] = useMutation(
        CREATE_CUSTOMISED_COMPONENT_FIELD,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.createCustomisedComponentField
                loadLogBookConfig()
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    const deleteLocalCustomisedComponentField = async (id: any) => {
        if (+id > 0) {
            const customisedComponentFieldModel =
                new CustomisedComponentFieldModel()
            await customisedComponentFieldModel.delete(id)
        }
    }

    const [updateCustomisedComponentField] = useMutation(
        UPDATE_CUSTOMISED_COMPONENT_FIELD,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.updateCustomisedComponentField
                deleteLocalCustomisedComponentField(data.id)
                if (resetCounter > 0) {
                    setResetCounter(resetCounter - 1)
                }
                if (resetCounter == 0) {
                    setResetCounter(resetCounter - 1)
                }
                loadLogBookConfig()
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    const handleUpdateConfigEdit = async (field: any, form: IConfigForm) => {
        document.body.style.cursor = 'wait'
        await updateCustomisedComponentField({
            variables: {
                input: {
                    id: field.id,
                    customisedFieldTitle: form.fieldName,
                    sortOrder: form.sortOrder,
                    description:
                        form.description === '<p><br></p>'
                            ? ''
                            : form.description,
                },
            },
        })

        editConfigDialog.closeDialog()
    }

    const handleSetDailyCheckCategories = () => {
        const logbookFields = slallFields

        const dailyCheckCategories = Array.from(
            new Set(
                logbookFields.filter((field: any) => field.subCategory).length >
                0
                    ? logbookFields
                          .filter((field: any) => field.subCategory)[0]
                          .items.filter((item: any) => item.level !== 3) // Exclude level 3 items
                          .map((item: any) => {
                              return item.fieldSet ? item.fieldSet : 'Other' // Map main categories
                          })
                    : 'Other',
            ),
        )

        setDailyCheckCategories(
            dailyCheckCategories.filter(
                (category: any) =>
                    category !== 'Checks' &&
                    category !== 'Other' &&
                    category !== 'Documentation' &&
                    category !== 'Fuel Checks',
            ),
        )
        if (!dailyCheckCategory) {
            setDailyCheckCategory(dailyCheckCategories[0])
        }
    }

    const handleLevelThreeCategories = () => {
        const logbookFields = slallFields
        const levelThreeCategories: any = Array.from(
            new Set(
                logbookFields
                    .filter((field: any) => field.subCategory)[0]
                    .items.filter((field: any) => field.level === 3)
                    .map((field: any) => {
                        return {
                            fieldSet: field.fieldSet,
                            label: field.label,
                            status: field.status,
                        }
                    }),
            ),
        )
        setLevelThreeCategories(levelThreeCategories)
        if (!levelThreeCategory) {
            setLevelThreeCategory(levelThreeCategories[0].label)
        }
    }

    const mapConfigToDefault = (currentComponent: any) => {
        const logbookFields = slallFields
        setResetCounter(resetCounter + 1)
        const config = logBookConfig.customisedLogBookComponents.nodes
            .filter((component: any) => component.id == currentComponent.id)
            .map((component: any) => component)

        const defaultConfig = logbookFields.map((component: any) => component)

        config.forEach((customisedLogBookComponents: any) => {
            defaultConfig.forEach((defaultLogBookComponents: any) => {
                if (
                    customisedLogBookComponents.componentClass ===
                    defaultLogBookComponents.componentClass
                ) {
                    customisedLogBookComponents.customisedComponentFields.nodes
                        .filter(
                            (customFields: any, index: number, self: any[]) =>
                                self.findIndex(
                                    (c: any) =>
                                        c.fieldName === customFields.fieldName,
                                ) === index,
                        )
                        .forEach((customFields: any) => {
                            defaultLogBookComponents.items.forEach(
                                (defaultField: any) => {
                                    if (
                                        customFields.fieldName ===
                                        defaultField.value
                                    ) {
                                        const updatedField = updatedFields.find(
                                            (updatedField: any) =>
                                                updatedField.fieldID ===
                                                customFields.id,
                                        )
                                        if (
                                            defaultField.status !=
                                            customFields.status
                                        ) {
                                            document.body.style.cursor = 'wait'
                                            updateCustomisedComponentField({
                                                variables: {
                                                    input: {
                                                        id: customFields.id,
                                                        status: defaultField.status,
                                                    },
                                                },
                                            })
                                            setResetCounter(resetCounter + 1)
                                        }
                                        if (
                                            updatedField?.fieldID &&
                                            updatedField?.status !=
                                                defaultField.status
                                        ) {
                                            document.body.style.cursor = 'wait'
                                            updateCustomisedComponentField({
                                                variables: {
                                                    input: {
                                                        id: customFields.id,
                                                        status: defaultField.status,
                                                    },
                                                },
                                            })
                                            setResetCounter(resetCounter + 1)
                                        }
                                    }
                                },
                            )
                        })
                }
            })
        })
    }

    const [updateCustomisedComponent] = useMutation(
        UPDATE_CUSTOMISED_LOGBOOK_COMPONENT,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.updateCustomisedLogBookComponent
                loadLogBookConfig()
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    const activateCustomisedComponent = (id: number) => {
        document.body.style.cursor = 'wait'
        updateCustomisedComponent({
            variables: {
                input: {
                    id: id,
                    active: true,
                },
            },
        })
    }

    const deactivateCustomisedComponent = (id: number) => {
        document.body.style.cursor = 'wait'
        updateCustomisedComponent({
            variables: {
                input: {
                    id: id,
                    active: false,
                },
            },
        })
    }

    const activateCustomisedSubComponent =
        (component: any, category: string) => () => {
            if (component.subFields) {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: component.subFields + '||' + category,
                        },
                    },
                })
            } else {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: dailyCheckCategories
                                .concat(category)
                                .join('||'),
                        },
                    },
                })
            }
        }

    const deactivateCustomisedSubComponent = (
        component: any,
        category: string,
    ) => {
        if (component.subFields) {
            document.body.style.cursor = 'wait'
            updateCustomisedComponent({
                variables: {
                    input: {
                        id: component.id,
                        subFields: component.subFields
                            .split('||')
                            .filter((field: any) => field !== category)
                            .join('||'),
                    },
                },
            })
        } else {
            document.body.style.cursor = 'wait'
            updateCustomisedComponent({
                variables: {
                    input: {
                        id: component.id,
                        subFields: dailyCheckCategories
                            .filter((cat: any) => cat != category)
                            .join('||'),
                    },
                },
            })
        }
    }

    const activateCustomisedLevelThreeComponent = (
        component: any,
        levelThree: any,
    ) => {
        if (component.subFields) {
            document.body.style.cursor = 'wait'
            updateCustomisedComponent({
                variables: {
                    input: {
                        id: component.id,
                        subFields:
                            component.subFields + '||' + levelThree.label,
                    },
                },
            })
        } else {
            document.body.style.cursor = 'wait'
            updateCustomisedComponent({
                variables: {
                    input: {
                        id: component.id,
                        subFields: dailyCheckCategories
                            .concat(levelThree.label)
                            .join('||'),
                    },
                },
            })
        }
        //if (levelThree?.status === 'disabled') {
        levelThreeCategories.map((category: any) => {
            if (category.label === levelThree.label) {
                category.status = 'Enabled'
            }
            return category
        })
        //}
    }

    const deactivateCustomisedLevelThreeComponent =
        (component: any, levelThree: any) => () => {
            if (component.subFields) {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: component.subFields
                                .split('||')
                                .filter(
                                    (field: any) => field !== levelThree.label,
                                )
                                .join('||'),
                        },
                    },
                })
            } else {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: dailyCheckCategories
                                .filter((cat: any) => cat != levelThree.label)
                                .join('||'),
                        },
                    },
                })
            }
            //if (levelThree?.status === 'enabled') {
            levelThreeCategories.map((category: any) => {
                if (category.label === levelThree.label) {
                    category.status = 'Disabled'
                }
                return category
            })
            //}
        }

    const [createCustomisedComponent] = useMutation(
        CREATE_CUSTOMISED_LOGBOOK_COMPONENT,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.createCustomisedComponent
                loadLogBookConfig()
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    const handleSave = (notify = true) => {
        if (notify) {
            setSaving(true)
            toast.loading('Logbook configuration saving...')
        } else {
            setNotify(false)
        }
        const policies =
            documents.length > 0
                ? documents?.map((doc: any) => +doc.id).join(',')
                : ''
        document.body.style.cursor = 'wait'
        updateCustomisedLogBookConfig({
            variables: {
                input: {
                    id: logBookConfig.id,
                    policies: policies,
                },
            },
        })
        setIsLoading(true)
    }

    useEffect(() => {
        if (!isLoading && documents) {
            setNotify(false)
            handleSave(false)
        }
    }, [documents])

    const deleteLocalCustomisedLogBookConfig = async (id: any) => {
        if (+id > 0) {
            const customisedLogBookConfigModel =
                new CustomisedLogBookConfigModel()
            await customisedLogBookConfigModel.delete(id)
        }
    }

    const [updateCustomisedLogBookConfig] = useMutation(
        UPDATE_CUSTOMISED_LOGBOOK_CONFIG,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.updateCustomisedLogBookConfig
                deleteLocalCustomisedLogBookConfig(data.id)
                if (data.id > 0) {
                    if (notify) {
                        toast.success('Logbook configuration saved')
                        setSaving(false)
                    }
                    setNotify(true)
                }
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    const customisedComponentFieldsCombinedFilter = (field: any): boolean => {
        if (
            field?.__typename &&
            field.__typename === 'CustomisedComponentField'
        ) {
            const logbookFields = slallFields
            const defaultConfig = logbookFields.map(
                (component: any) => component,
            )
            var group = false
            var returnField = false
            var groupFields = false
            defaultConfig.forEach((defaultLogBookComponents: any) => {
                if (
                    tab?.replace('Logbook', 'LogBook') ===
                        defaultLogBookComponents.label.replace(
                            'Logbook',
                            'LogBook',
                        ) ||
                    (defaultLogBookComponents.label === 'Crew Welfare' &&
                        tab === 'Crew Members')
                ) {
                    if (
                        defaultLogBookComponents.items.find(
                            (defaultField: any) =>
                                defaultField.groupTo === field.fieldName,
                        ) !== undefined
                    ) {
                        groupFields = true
                    }
                    defaultLogBookComponents.items.forEach(
                        (defaultField: any) => {
                            if (field.fieldName === defaultField.value) {
                                group = defaultField?.groupTo
                                    ? defaultField.groupTo
                                    : false
                            }
                            if (
                                field.fieldName === defaultField.value &&
                                defaultField.vesselType.includes(
                                    vesselTypes.indexOf(vessel?.vesselType),
                                ) &&
                                defaultField.level !== 3
                            ) {
                                returnField = field
                            }
                        },
                    )
                }
            })
            return (
                group === false && returnField !== false && groupFields == false
            )
        } else {
            const logbookFields = slallFields
            const defaultConfig = logbookFields.map(
                (component: any) => component,
            )
            var group = false
            var returnField = false
            var groupFields = false
            defaultConfig.forEach((defaultLogBookComponents: any) => {
                if (
                    tab?.replace('Logbook', 'LogBook') ===
                        defaultLogBookComponents.label.replace(
                            'Logbook',
                            'LogBook',
                        ) ||
                    (defaultLogBookComponents.label === 'Crew Welfare' &&
                        tab === 'Crew Members')
                ) {
                    if (
                        defaultLogBookComponents.items.find(
                            (defaultField: any) =>
                                defaultField.groupTo === field.value,
                        ) !== undefined
                    ) {
                        groupFields = true
                    }
                    defaultLogBookComponents.items.forEach(
                        (defaultField: any) => {
                            if (field.value === defaultField.value) {
                                group = defaultField?.groupTo
                                    ? defaultField.groupTo
                                    : false
                            }
                            if (
                                field.value === defaultField.value &&
                                defaultField.vesselType.includes(
                                    vesselTypes.indexOf(vessel?.vesselType),
                                ) &&
                                defaultField.level !== 3
                            ) {
                                returnField = field
                            }
                        },
                    )
                }
            })
            return (
                group === false && returnField !== false && groupFields == false
            )
        }
    }

    useEffect(() => {
        if (!tab && Array.isArray(tabs) && tabs.length > 0) {
            setTab(tabs[0].title)
        }
    }, [tab, tabs])

    const filteredTabs = useMemo(() => {
        return tabs.filter(
            (element) =>
                element.title !== 'Crew Welfare' &&
                element.title !== 'Crew Training' &&
                element.componentClass !== 'Engine_LogBookComponent' &&
                element.componentClass !== 'Engineer_LogBookComponent' &&
                element.componentClass !== 'Fuel_LogBookComponent' &&
                element.componentClass !== 'Supernumerary_LogBookComponent',
        )
    }, [tabs])

    const componentsNotInConfig = useMemo(() => {
        const fields = logBookConfig
            ? getComponentsNotInConfig(slallFields, logBookConfig) ?? []
            : []
        return fields
    }, [logBookConfig, slallFields])

    const sortedCustomisedLogBookComponents = useMemo(() => {
        if (!logBookConfig) {
            return []
        }

        const elements = [...logBookConfig.customisedLogBookComponents?.nodes]
        // Crew Members must come first before Crew Welfare
        return elements.sort((a: any, b: any) => {
            if (a.componentClass === 'CrewMembers_LogBookComponent') return -1
            if (b.componentClass === 'CrewMembers_LogBookComponent') return 1
            return 0
        })
    }, [logBookConfig])

    console.info('Tab', tab, tabs)
    console.info(
        'Daily Check Category',
        dailyCheckCategory,
        dailyCheckCategories,
    )
    console.info(
        'Level Three Category',
        levelThreeCategory,
        levelThreeCategories,
    )

    return (
        <div className="space-y-6">
            <ListHeader title={`Logbook Configuration: ${vessel?.title}`} />
            <Card>
                <CardHeader>
                    <CardTitle className="text-2xl font-semibold">
                        Vessel Type: {vessel?.vesselType?.replaceAll('_', ' ')}
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {!isLoading && filteredTabs.length > 0 && (
                        <>
                            <Tabs
                                value={tab}
                                onValueChange={(newTab) => {
                                    setTab(newTab)
                                }}>
                                <TabsList>
                                    {filteredTabs.map(
                                        (element, index: number) => {
                                            return (
                                                <TabsTrigger
                                                    key={element.title}
                                                    value={element.title}>
                                                    {getTabTitle(
                                                        element,
                                                        SLALL_LogBookFields,
                                                    )}
                                                </TabsTrigger>
                                            )
                                        },
                                    )}
                                </TabsList>
                                <div className="mt-4 flex flex-col gap-4">
                                    {filteredTabs.map((element) => (
                                        <TabsContent
                                            value={element.title}
                                            key={element.title}>
                                            <LogBookConfigTabContent
                                                tabValue={element.title}
                                            />

                                            {/* {logBookConfig && (
                                                <ComponentsNotInConfig
                                                    tab={element.title}
                                                    logBookConfig={
                                                        logBookConfig
                                                    }
                                                    slallFields={slallFields}
                                                    fields={
                                                        componentsNotInConfig
                                                    }
                                                    imCrew={imCrew}
                                                    createCustomisedComponent={
                                                        createCustomisedComponent
                                                    }
                                                />
                                            )} */}
                                        </TabsContent>
                                    ))}
                                </div>
                            </Tabs>
                        </>
                    )}
                </CardContent>
            </Card>
            <FooterWrapper>
                <Button
                    size="sm"
                    variant="back"
                    onClick={() => {
                        router.back()
                    }}>
                    Cancel
                </Button>
                {!isLoading &&
                    tabs &&
                    logBookConfig.customisedLogBookComponents?.nodes?.length >
                        0 &&
                    logBookConfig.customisedLogBookComponents?.nodes?.map(
                        (component: any) => (
                            <div
                                key={component.id}
                                className={`${tab?.replace('Logbook', 'LogBook') === component.title.replace('Logbook', 'LogBook') ? '' : 'hidden'}`}>
                                {isCategorised(
                                    component,
                                    slallFields,
                                    logBookConfig,
                                ) && (
                                    <>
                                        {component.active ? (
                                            <>
                                                {!imCrew && (
                                                    <>
                                                        <Button
                                                            size="sm"
                                                            className="mr-2"
                                                            variant="destructive"
                                                            iconLeft={BanIcon}
                                                            onClick={() =>
                                                                deactivateCustomisedComponent(
                                                                    component.id,
                                                                )
                                                            }>
                                                            Disable
                                                        </Button>
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            iconLeft={RotateCcw}
                                                            onClick={() =>
                                                                mapConfigToDefault(
                                                                    component,
                                                                )
                                                            }>
                                                            Reset to default
                                                        </Button>
                                                    </>
                                                )}
                                            </>
                                        ) : (
                                            <>
                                                {!imCrew && (
                                                    <Button
                                                        size="sm"
                                                        variant="primary"
                                                        onClick={() =>
                                                            activateCustomisedComponent(
                                                                component.id,
                                                            )
                                                        }>
                                                        Enable
                                                    </Button>
                                                )}
                                            </>
                                        )}
                                    </>
                                )}
                            </div>
                        ),
                    )}
                {!isLoading &&
                    tabs &&
                    logBookConfig &&
                    componentsNotInConfig.map((component: any) => (
                        <div
                            key={component.label}
                            className={`${tab?.replace('Logbook', 'LogBook') === component.label.replace('Logbook', 'LogBook') ? '' : 'hidden'}`}>
                            {!imCrew && (
                                <Button
                                    size="sm"
                                    variant="primaryOutline"
                                    iconLeft={FilePlusIcon}
                                    onClick={() => {
                                        document.body.style.cursor = 'wait'
                                        createCustomisedComponent({
                                            variables: {
                                                input: {
                                                    title: component.label,
                                                    sortOrder:
                                                        component.sortOrder ||
                                                        0,
                                                    category:
                                                        component.category,
                                                    customisedLogBookConfigID:
                                                        logBookConfig.id,
                                                    componentClass:
                                                        component.componentClass,
                                                    active: true,
                                                },
                                            },
                                        })
                                    }}>
                                    {`Add ${component.label}`}
                                </Button>
                            )}
                        </div>
                    ))}
                {!imCrew && (
                    <Button
                        size="sm"
                        variant="primary"
                        onClick={() => handleSave()}
                        disabled={saving}>
                        {saving ? 'Saving' : 'Save'}
                    </Button>
                )}
            </FooterWrapper>

            <EditConfigDialog
                title={editConfigDialog.title}
                field={editConfigDialog.selectedField}
                isFieldGroup={editConfigDialog.isFieldGroup}
                defaultValues={editConfigDialog.form}
                isOpen={editConfigDialog.isOpen}
                onOpenChange={editConfigDialog.onOpenChange}
                onSave={(field, form) => handleUpdateConfigEdit(field, form)}
            />

            <DescriptionDialog
                open={descriptionDialog.open}
                onOpenChange={descriptionDialog.onOpenChange}
                title={descriptionDialog.title}
                content={descriptionDialog.content}
            />
        </div>
    )
}
