import { useEffect, useState } from 'react'
import {
    ReadAppNotificationRecipients,
    UpdateAppNotificationRecipient,
    DeleteAppNotificationRecipients,
} from './queries'
import { useLazyQuery, useMutation } from '@apollo/client'
import {
    createColumns,
    DataTable,
    ExtendedColumnDef,
} from '@/components/filteredTable'
import Link from 'next/link'
import { P, Button, AlertDialogNew } from '@/components/ui'
import { Eye, EyeOff, Trash2 } from 'lucide-react'
import { toast } from 'sonner'

const AppNotification = () => {
    const [notifications, setNotifications] = useState([])
    const [userID, setUserID] = useState(0)
    const [deleteConfirmation, setDeleteConfirmation] = useState(false)
    const [notificationToDelete, setNotificationToDelete] = useState<any>(null)

    const [readAppNotificationRecipients] = useLazyQuery(
        ReadAppNotificationRecipients,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readAppNotificationRecipients
                if (data) {
                    setNotifications(data.nodes)
                }
            },
            onError: (error: any) => {
                console.error('readAppNotificationRecipients error', error)
            },
        },
    )

    const [updateAppNotificationRecipient] = useMutation(
        UpdateAppNotificationRecipient,
        {
            onCompleted: () => {
                // toast.success('Notification updated successfully')
                loadNotifications()
            },
            onError: (error: any) => {
                console.error('updateAppNotificationRecipient error', error)
                toast.error('Failed to update notification')
            },
        },
    )

    const [deleteAppNotificationRecipient] = useMutation(
        DeleteAppNotificationRecipients,
        {
            onCompleted: () => {
                // toast.success('Notification deleted successfully')
                loadNotifications()
                setDeleteConfirmation(false)
                setNotificationToDelete(null)
            },
            onError: (error: any) => {
                console.error('deleteAppNotificationRecipient error', error)
                toast.error('Failed to delete notification')
            },
        },
    )
    const loadNotifications = async () => {
        await readAppNotificationRecipients({
            variables: {
                filter: { userID: { eq: userID } },
            },
        })
    }

    const handleToggleReadStatus = async (notification: any) => {
        await updateAppNotificationRecipient({
            variables: {
                input: {
                    id: notification.id,
                    isRead: !notification.isRead,
                },
            },
        })
    }

    const handleDeleteClick = (notification: any) => {
        setNotificationToDelete(notification)
        setDeleteConfirmation(true)
    }

    const handleDeleteConfirm = async () => {
        if (notificationToDelete) {
            await deleteAppNotificationRecipient({
                variables: {
                    ids: [notificationToDelete.id],
                },
            })
        }
    }

    useEffect(() => {
        setUserID(+(localStorage.getItem('userId') ?? 0))
    }, [])
    useEffect(() => {
        if (userID > 0) {
            loadNotifications()
        }
    }, [userID])

    // Define columns for the notifications table
    const columns: ExtendedColumnDef<any, any>[] = createColumns([
        {
            accessorKey: 'title',
            header: 'Notification',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return (
                    <div className="space-y-1 py-2.5">
                        <div className="flex items-start justify-between">
                            <span className="flex flex-col items-center sm:flex-row gap-x-2.5">
                                {item.notification.targetLink ? (
                                    <Link href={item.notification.targetLink}>
                                        <div
                                            className={`hover:underline ${!item.isRead ? 'font-semibold' : ''}`}>
                                            {item.notification.message}
                                        </div>
                                    </Link>
                                ) : (
                                    <div
                                        className={
                                            !item.isRead ? 'font-semibold' : ''
                                        }>
                                        {item.notification.message}
                                    </div>
                                )}
                            </span>
                        </div>
                        <P className={!item.isRead ? 'font-semibold' : ''}>
                            {item.notification.title}
                        </P>
                    </div>
                )
            },
        },
        {
            accessorKey: 'actions',
            header: 'Actions',
            cellAlignment: 'center',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return (
                    <div className="flex items-center justify-center gap-2">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleToggleReadStatus(item)}
                            className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-600"
                            aria-label={
                                item.isRead ? 'Mark as unread' : 'Mark as read'
                            }>
                            {item.isRead ? (
                                <EyeOff className="h-4 w-4" />
                            ) : (
                                <Eye className="h-4 w-4" />
                            )}
                        </Button>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteClick(item)}
                            className="h-8 w-8 p-0 hover:bg-destructive/10 hover:text-destructive"
                            aria-label="Delete notification">
                            <Trash2 className="h-4 w-4" />
                        </Button>
                    </div>
                )
            },
        },
    ])
    return (
        <div>
            {notifications.length > 0 && (
                <DataTable
                    columns={columns}
                    data={notifications}
                    showToolbar={false}
                    pageSize={20}
                />
            )}

            {/* Delete Confirmation Dialog */}
            <AlertDialogNew
                openDialog={deleteConfirmation}
                setOpenDialog={setDeleteConfirmation}
                title="Delete Notification"
                description={`Are you sure you want to delete this notification? This action cannot be undone.`}
                handleDestructiveAction={handleDeleteConfirm}
                destructiveActionText="Delete"
                cancelText="Cancel"
                variant="danger"
                showDestructiveAction={true}
            />
        </div>
    )
}

export default AppNotification
