import { useBreakpoints } from "@/components/hooks/useBreakpoints"

type BreakpointKey =
    | 'tiny'
    | 'small'
    | 'standard'
    | 'phablet'
    | 'tablet-sm'
    | 'tablet-md'
    | 'tablet-lg'
    | 'landscape'
    | 'laptop'
    | 'desktop'

// Type for multiple breakpoint text configuration
type ResponsiveTextConfig = Partial<Record<BreakpointKey, string>>

// Enhanced hook version - supports both legacy and multi-breakpoint usage
function useResponsiveLabel(breakpoint: BreakpointKey = 'phablet') {
    const bp = useBreakpoints()

    // Legacy function signature for backward compatibility
    function legacyResponsiveLabel(short: string, long: string): string {
        return bp[breakpoint] ? long : short
    }

    // Enhanced function signature for multi-breakpoint support
    function multiBreakpointResponsiveLabel(config: ResponsiveTextConfig): string {
        // Get breakpoints in order from largest to smallest for proper cascading
        const breakpointOrder: BreakpointKey[] = [
            'desktop',
            'laptop',
            'landscape',
            'tablet-lg',
            'tablet-md',
            'tablet-sm',
            'phablet',
            'standard',
            'small',
            'tiny'
        ]

        // Find the first (largest) active breakpoint that has text defined
        for (const bpKey of breakpointOrder) {
            if (bp[bpKey] && config[bpKey]) {
                return config[bpKey]!
            }
        }

        // Fallback: return the first available text value if no breakpoint matches
        const availableTexts = Object.values(config).filter(Boolean)
        return availableTexts[0] || ''
    }

    // Return overloaded function that handles both signatures
    return function responsiveLabel(
        shortOrConfig: string | ResponsiveTextConfig,
        long?: string
    ): string {
        // If second parameter is provided, use legacy behavior
        if (typeof shortOrConfig === 'string' && long !== undefined) {
            return legacyResponsiveLabel(shortOrConfig, long)
        }

        // If first parameter is an object, use multi-breakpoint behavior
        if (typeof shortOrConfig === 'object' && shortOrConfig !== null) {
            return multiBreakpointResponsiveLabel(shortOrConfig)
        }

        // Fallback for edge cases
        return String(shortOrConfig)
    }
}

// Utility version - use this when you have breakpoint state available
function getResponsiveLabel(isAtBreakpoint: boolean, short: string, long: string) {
    return isAtBreakpoint ? long : short
}

// Enhanced utility version for multi-breakpoint support
function getMultiBreakpointLabel(
    breakpoints: Record<BreakpointKey, boolean>,
    config: ResponsiveTextConfig
): string {
    // Get breakpoints in order from largest to smallest for proper cascading
    const breakpointOrder: BreakpointKey[] = [
        'desktop',
        'laptop',
        'landscape',
        'tablet-lg',
        'tablet-md',
        'tablet-sm',
        'phablet',
        'standard',
        'small',
        'tiny'
    ]

    // Find the first (largest) active breakpoint that has text defined
    for (const bpKey of breakpointOrder) {
        if (breakpoints[bpKey] && config[bpKey]) {
            return config[bpKey]!
        }
    }

    // Fallback: return the first available text value if no breakpoint matches
    const availableTexts = Object.values(config).filter(Boolean)
    return availableTexts[0] || ''
}

export {
    useResponsiveLabel,
    getResponsiveLabel,
    getMultiBreakpointLabel,
    type BreakpointKey,
    type ResponsiveTextConfig
}
